<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brutalist Header</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=EB+Garamond:wght@400;700&family=JetBrains+Mono:wght@400;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --black: #000000;
            --white: #ffffff;
            --accent: #d32f2f;
            --gray-light: #f5f5f5;
            --gray-dark: #1a1a1a;
        }

        body {
            font-family: '<PERSON><PERSON> Garamond', serif;
            background: var(--white);
            color: var(--black);
            overflow-x: hidden;
        }

        /* Header Container */
        header {
            background: var(--white);
            border-bottom: 2px solid var(--black);
            padding: 2.5rem 3rem;
            position: relative;
            animation: headerSlide 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        @keyframes headerSlide {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Diagonal accent line */
        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 150%;
            height: 4px;
            background: linear-gradient(90deg, var(--accent) 0%, var(--accent) 30%, transparent 100%);
            transform: skewX(-15deg);
        }

        /* Main header content grid */
        .header-content {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 4rem;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Logo/Brand */
        .brand {
            display: flex;
            align-items: baseline;
            gap: 0.75rem;
            animation: brandFade 1s ease-out 0.2s both;
        }

        @keyframes brandFade {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .brand-symbol {
            width: 32px;
            height: 32px;
            background: var(--black);
            position: relative;
            transform: rotate(45deg);
        }

        .brand-symbol::after {
            content: '';
            position: absolute;
            inset: 6px;
            background: var(--white);
            border: 2px solid var(--accent);
        }

        .brand-name {
            font-size: 1.5rem;
            font-weight: 700;
            letter-spacing: -1px;
            line-height: 1;
        }

        .brand-descriptor {
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.65rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: var(--accent);
            font-weight: 600;
            display: block;
            margin-top: 0.25rem;
        }

        /* Navigation */
        nav {
            display: flex;
            gap: 3rem;
            animation: navFade 1s ease-out 0.4s both;
        }

        @keyframes navFade {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        nav a {
            position: relative;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: var(--black);
            text-decoration: none;
            transition: color 0.3s ease;
            padding-bottom: 2px;
        }

        nav a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--accent);
            transition: width 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        nav a:hover {
            color: var(--accent);
        }

        nav a:hover::after {
            width: 100%;
        }

        /* CTA Button */
        .cta-button {
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            background: var(--black);
            color: var(--white);
            border: 2px solid var(--black);
            padding: 0.9rem 1.8rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            position: relative;
            overflow: hidden;
            animation: ctaFade 1s ease-out 0.6s both;
        }

        @keyframes ctaFade {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--accent);
            transition: left 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            z-index: -1;
        }

        .cta-button:hover {
            background: var(--accent);
            border-color: var(--accent);
            transform: translate(2px, -2px);
            box-shadow: -4px 4px 0 var(--black);
        }

        /* Responsive */
        @media (max-width: 768px) {
            header {
                padding: 2rem 1.5rem;
            }

            .header-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            nav {
                gap: 1.5rem;
                flex-wrap: wrap;
            }

            nav a {
                font-size: 0.75rem;
                letter-spacing: 0.5px;
            }

            .brand-name {
                font-size: 1.25rem;
            }
        }

        /* Demo section for context */
        .demo-section {
            padding: 4rem 3rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .demo-section h1 {
            font-size: 3.5rem;
            line-height: 1.1;
            margin-bottom: 2rem;
            letter-spacing: -2px;
            animation: contentSlide 1s ease-out 0.8s both;
        }

        @keyframes contentSlide {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .demo-section p {
            font-size: 1.1rem;
            line-height: 1.8;
            max-width: 600px;
            color: var(--gray-dark);
        }

        .accent-line {
            width: 60px;
            height: 4px;
            background: var(--accent);
            margin-bottom: 1.5rem;
            animation: lineSlide 1s ease-out 0.4s both;
        }

        @keyframes lineSlide {
            from {
                opacity: 0;
                width: 0;
            }
            to {
                opacity: 1;
                width: 60px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="brand">
                <div class="brand-symbol"></div>
                <div>
                    <div class="brand-name">NEXUS</div>
                    <span class="brand-descriptor">Design Studio</span>
                </div>
            </div>
            <nav>
                <a href="#work">Work</a>
                <a href="#about">About</a>
                <a href="#services">Services</a>
                <a href="#contact">Contact</a>
            </nav>
        </div>
        <button class="cta-button">Get Started</button>
    </header>

    <section class="demo-section">
        <div class="accent-line"></div>
        <h1>Stripped. Refined. Intentional.</h1>
        <p>A brutalist header that rejects ornament in favor of pure architectural form. Asymmetrical grid, diagonal accents, and deliberate motion create a memorable first impression. Every detail serves a purpose.</p>
    </section>
</body>
</html>
