<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Theme Header Component</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=EB+Garamond:wght@400;700&family=JetBrains+Mono:wght@400;600&family=Playfair+Display:wght@700&family=Poppins:wght@400;600;700&family=Space+Mono:wght@400;700&family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            transition: background-color 0.6s ease, color 0.6s ease;
            font-family: 'Poppins', sans-serif;
            overflow-x: hidden;
        }

        /* ========== THEME SWITCHER ========== */
        .theme-switcher {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .theme-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid currentColor;
            background: transparent;
            cursor: pointer;
            font-size: 0.7rem;
            font-weight: 700;
            text-transform: uppercase;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .theme-btn:hover {
            transform: scale(1.1);
        }

        .theme-btn.active {
            background: currentColor;
            color: var(--bg);
        }

        @media (max-width: 768px) {
            .theme-switcher {
                bottom: 1rem;
                right: 1rem;
            }
            .theme-btn {
                width: 40px;
                height: 40px;
                font-size: 0.6rem;
            }
        }

        /* ========== BRUTALIST THEME ========== */
        body.theme-brutalist {
            --bg: #ffffff;
            --text: #000000;
            --accent: #d32f2f;
            --secondary: #1a1a1a;
        }

        body.theme-brutalist header {
            background: var(--bg);
            border-bottom: 3px solid var(--text);
            padding: 2.5rem 3rem;
        }

        body.theme-brutalist header::before {
            background: linear-gradient(90deg, var(--accent) 0%, var(--accent) 30%, transparent 100%);
            height: 4px;
            transform: skewX(-15deg);
        }

        body.theme-brutalist .brand-name {
            font-family: 'EB Garamond', serif;
            font-size: 1.5rem;
            font-weight: 700;
            letter-spacing: -1px;
        }

        body.theme-brutalist nav a {
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        body.theme-brutalist .cta-button {
            background: var(--text);
            color: var(--bg);
            border: 2px solid var(--text);
        }

        body.theme-brutalist .cta-button:hover {
            background: var(--accent);
            border-color: var(--accent);
            transform: translate(2px, -2px);
            box-shadow: -4px 4px 0 var(--text);
        }

        /* ========== MINIMALIST THEME ========== */
        body.theme-minimalist {
            --bg: #fafafa;
            --text: #2a2a2a;
            --accent: #0066cc;
            --secondary: #999999;
        }

        body.theme-minimalist header {
            background: var(--bg);
            border: none;
            border-bottom: 1px solid #e0e0e0;
            padding: 1.5rem 3rem;
        }

        body.theme-minimalist header::before {
            display: none;
        }

        body.theme-minimalist .brand-name {
            font-family: 'Poppins', sans-serif;
            font-size: 1.2rem;
            font-weight: 700;
            letter-spacing: 0;
        }

        body.theme-minimalist .brand-descriptor {
            display: none;
        }

        body.theme-minimalist nav a {
            font-family: 'Poppins', sans-serif;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: capitalize;
            letter-spacing: 0;
        }

        body.theme-minimalist nav a::after {
            height: 1px;
        }

        body.theme-minimalist .cta-button {
            background: var(--accent);
            color: white;
            border: none;
            font-size: 0.8rem;
            padding: 0.7rem 1.4rem;
            border-radius: 4px;
        }

        body.theme-minimalist .cta-button:hover {
            background: #0052a3;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 102, 204, 0.2);
        }

        /* ========== GLASSMORPHISM THEME ========== */
        body.theme-glass {
            --bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --text: #ffffff;
            --accent: #00d4ff;
            --secondary: rgba(255, 255, 255, 0.3);
        }

        body.theme-glass {
            background: var(--bg) fixed;
            min-height: 100vh;
        }

        body.theme-glass header {
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 3rem;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        body.theme-glass header::before {
            display: none;
        }

        body.theme-glass .brand-name {
            font-family: 'Playfair Display', serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
        }

        body.theme-glass .brand-descriptor {
            color: var(--accent);
        }

        body.theme-glass nav a {
            font-family: 'Poppins', sans-serif;
            font-size: 0.9rem;
            font-weight: 500;
            color: white;
        }

        body.theme-glass nav a::after {
            background: var(--accent);
        }

        body.theme-glass .cta-button {
            background: var(--accent);
            color: #667eea;
            border: 2px solid var(--accent);
            font-weight: 700;
        }

        body.theme-glass .cta-button:hover {
            background: transparent;
            transform: scale(1.05);
            box-shadow: 0 8px 32px rgba(0, 212, 255, 0.3);
        }

        /* ========== ART DECO THEME ========== */
        body.theme-deco {
            --bg: #0a0a0a;
            --text: #ffd700;
            --accent: #ffffff;
            --secondary: #1a1a1a;
        }

        body.theme-deco {
            background: var(--bg);
        }

        body.theme-deco header {
            background: var(--bg);
            border-bottom: none;
            border-top: 3px solid var(--text);
            border-bottom: 3px solid var(--text);
            padding: 2.5rem 3rem;
            position: relative;
        }

        body.theme-deco header::before {
            background: linear-gradient(45deg, transparent 0%, var(--text) 50%, transparent 100%);
            height: 2px;
            top: -8px;
        }

        body.theme-deco header::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(45deg, transparent 0%, var(--text) 50%, transparent 100%);
        }

        body.theme-deco .brand-name {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            font-weight: 700;
            letter-spacing: 2px;
            color: var(--text);
        }

        body.theme-deco .brand-descriptor {
            font-size: 0.7rem;
            color: var(--accent);
            letter-spacing: 3px;
        }

        body.theme-deco nav a {
            font-family: 'Space Mono', monospace;
            font-size: 0.8rem;
            text-transform: uppercase;
            color: var(--text);
            letter-spacing: 1.5px;
        }

        body.theme-deco nav a::after {
            background: var(--text);
        }

        body.theme-deco .cta-button {
            background: var(--text);
            color: var(--bg);
            border: 2px solid var(--text);
            font-weight: 700;
        }

        body.theme-deco .cta-button:hover {
            background: transparent;
            color: var(--text);
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        /* ========== RETRO NEON THEME ========== */
        body.theme-retro {
            --bg: #0a0e27;
            --text: #00ff00;
            --accent: #ff006e;
            --secondary: #00d9ff;
        }

        body.theme-retro {
            background: var(--bg);
        }

        body.theme-retro header {
            background: var(--bg);
            border-bottom: 2px solid var(--secondary);
            padding: 2rem 3rem;
            position: relative;
            box-shadow: 0 0 20px rgba(0, 217, 255, 0.2);
        }

        body.theme-retro header::before {
            background: linear-gradient(90deg, var(--accent) 0%, var(--secondary) 100%);
            height: 2px;
        }

        body.theme-retro .brand-name {
            font-family: 'Courier Prime', monospace;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text);
            text-shadow: 0 0 10px var(--text);
        }

        body.theme-retro .brand-descriptor {
            color: var(--secondary);
            text-shadow: 0 0 5px var(--secondary);
        }

        body.theme-retro nav a {
            font-family: 'Courier Prime', monospace;
            font-size: 0.85rem;
            font-weight: 700;
            color: var(--secondary);
            text-transform: uppercase;
            text-shadow: 0 0 8px var(--secondary);
        }

        body.theme-retro nav a::after {
            background: var(--accent);
            box-shadow: 0 0 10px var(--accent);
        }

        body.theme-retro .cta-button {
            background: transparent;
            color: var(--accent);
            border: 2px solid var(--accent);
            font-family: 'Courier Prime', monospace;
            text-shadow: 0 0 10px var(--accent);
            box-shadow: 0 0 10px rgba(255, 0, 110, 0.3);
        }

        body.theme-retro .cta-button:hover {
            background: var(--accent);
            color: var(--bg);
            text-shadow: none;
            box-shadow: 0 0 30px var(--accent);
        }

        /* ========== LUXURY THEME ========== */
        body.theme-luxury {
            --bg: #1a1410;
            --text: #f5e6d3;
            --accent: #c9a961;
            --secondary: #8b7355;
        }

        body.theme-luxury {
            background: var(--bg);
        }

        body.theme-luxury header {
            background: var(--bg);
            border-bottom: 2px solid var(--accent);
            padding: 3rem;
            position: relative;
        }

        body.theme-luxury header::before {
            background: linear-gradient(90deg, var(--accent) 0%, transparent 100%);
            height: 1px;
        }

        body.theme-luxury .brand-symbol {
            background: var(--accent);
        }

        body.theme-luxury .brand-symbol::after {
            border-color: var(--accent);
        }

        body.theme-luxury .brand-name {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            font-weight: 700;
            letter-spacing: 1px;
            color: var(--text);
        }

        body.theme-luxury .brand-descriptor {
            font-family: 'Poppins', sans-serif;
            font-size: 0.65rem;
            color: var(--accent);
            letter-spacing: 2px;
        }

        body.theme-luxury nav a {
            font-family: 'Playfair Display', serif;
            font-size: 1rem;
            font-weight: 700;
            color: var(--text);
            text-transform: capitalize;
            letter-spacing: 0;
        }

        body.theme-luxury nav a::after {
            background: var(--accent);
            height: 1px;
        }

        body.theme-luxury .cta-button {
            background: transparent;
            color: var(--accent);
            border: 1px solid var(--accent);
            font-family: 'Playfair Display', serif;
            font-size: 0.9rem;
            letter-spacing: 1px;
            padding: 1rem 2rem;
        }

        body.theme-luxury .cta-button:hover {
            background: var(--accent);
            color: var(--bg);
            transform: scale(1.05);
            box-shadow: 0 8px 24px rgba(201, 169, 97, 0.2);
        }

        /* ========== SHARED STYLES ========== */
        header {
            transition: all 0.6s ease;
            animation: headerSlide 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        @keyframes headerSlide {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header-content {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 3rem;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .brand {
            display: flex;
            align-items: baseline;
            gap: 0.75rem;
            animation: brandFade 1s ease-out 0.2s both;
        }

        @keyframes brandFade {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .brand-symbol {
            width: 32px;
            height: 32px;
            background: currentColor;
            position: relative;
            transform: rotate(45deg);
            transition: all 0.3s ease;
        }

        .brand-symbol::after {
            content: '';
            position: absolute;
            inset: 6px;
            background: transparent;
            border: 2px solid currentColor;
        }

        .brand-descriptor {
            display: block;
            margin-top: 0.25rem;
            font-weight: 600;
            font-size: 0.65rem;
            text-transform: uppercase;
            transition: all 0.3s ease;
        }

        nav {
            display: flex;
            gap: 2.5rem;
            animation: navFade 1s ease-out 0.4s both;
        }

        @keyframes navFade {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        nav a {
            position: relative;
            text-decoration: none;
            color: inherit;
            transition: color 0.3s ease;
            padding-bottom: 2px;
        }

        nav a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            transition: width 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        nav a:hover::after {
            width: 100%;
        }

        .cta-button {
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            padding: 0.9rem 1.8rem;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            animation: ctaFade 1s ease-out 0.6s both;
            transition: all 0.3s ease;
        }

        @keyframes ctaFade {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* ========== MOBILE RESPONSIVE ========== */
        @media (max-width: 1024px) {
            .header-content {
                grid-template-columns: 1fr auto;
                gap: 2rem;
            }

            nav {
                gap: 1.5rem;
            }

            header {
                padding: 2rem;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .brand {
                justify-content: space-between;
                width: 100%;
            }

            nav {
                gap: 1rem;
                flex-wrap: wrap;
                order: 3;
                width: 100%;
            }

            nav a {
                font-size: 0.8rem !important;
                flex: 1;
                min-width: 45%;
                text-align: center;
            }

            .cta-button {
                width: 100%;
                padding: 0.8rem 1rem !important;
                order: 4;
            }

            header {
                padding: 1.5rem;
            }

            body.theme-brutalist header {
                padding: 1.5rem;
            }

            body.theme-glass header {
                padding: 1.5rem;
            }

            body.theme-luxury header {
                padding: 1.5rem;
            }

            .brand-name {
                font-size: 1rem !important;
            }

            .brand-symbol {
                width: 24px !important;
                height: 24px !important;
            }
        }

        @media (max-width: 480px) {
            header {
                padding: 1rem;
            }

            .header-content {
                gap: 1rem;
            }

            .brand-name {
                font-size: 0.9rem !important;
            }

            .brand-symbol {
                width: 20px !important;
                height: 20px !important;
            }

            nav {
                gap: 0.5rem;
            }

            nav a {
                font-size: 0.7rem !important;
                min-width: 30% !important;
            }

            .cta-button {
                font-size: 0.65rem !important;
                padding: 0.6rem 0.8rem !important;
            }
        }
    </style>
</head>
<body class="theme-brutalist">
    <header>
        <div class="header-content">
            <div class="brand">
                <div class="brand-symbol"></div>
                <div>
                    <div class="brand-name">NEXUS</div>
                    <span class="brand-descriptor">Design Studio</span>
                </div>
            </div>
            <nav>
                <a href="#work">Work</a>
                <a href="#about">About</a>
                <a href="#services">Services</a>
                <a href="#contact">Contact</a>
            </nav>
            <button class="cta-button">Get Started</button>
        </div>
    </header>

    <!-- Theme Switcher -->
    <div class="theme-switcher">
        <button class="theme-btn active" data-theme="brutalist" title="Brutalist">Brut</button>
        <button class="theme-btn" data-theme="minimalist" title="Minimalist">Mini</button>
        <button class="theme-btn" data-theme="glass" title="Glass">Glass</button>
        <button class="theme-btn" data-theme="deco" title="Art Deco">Deco</button>
        <button class="theme-btn" data-theme="retro" title="Retro Neon">Retro</button>
        <button class="theme-btn" data-theme="luxury" title="Luxury">Lux</button>
    </div>

    <script>
        const themeBtns = document.querySelectorAll('.theme-btn');
        const body = document.body;

        themeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const theme = btn.getAttribute('data-theme');
                
                // Remove old theme
                body.className = '';
                
                // Add new theme
                body.classList.add(`theme-${theme}`);
                
                // Update active button
                themeBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // Save preference
                localStorage.setItem('selectedTheme', theme);
            });
        });

        // Load saved theme
        const savedTheme = localStorage.getItem('selectedTheme') || 'brutalist';
        document.querySelector(`[data-theme="${savedTheme}"]`).click();
    </script>
</body>
</html>
